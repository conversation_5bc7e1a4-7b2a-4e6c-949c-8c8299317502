class_name Main
extends Node3D

## Main scene controller managing game flow and system coordination
## Implements the Facade pattern for system interactions

@onready var player: Player = $Player
@onready var enemy_spawner: Node = $GameSystems/EnemySpawner
@onready var loot_manager: Node = $GameSystems/LootManager
@onready var wave_manager: Node = $GameSystems/WaveManager
@onready var skill_tree: Node = $GameSystems/SkillTree
@onready var effect_manager: Node = $GameSystems/EffectManager
@onready var audio_manager: Node = $GameSystems/AudioManager
@onready var biome_manager: Node = $GameSystems/BiomeManager
@onready var hud: Control = $UI/HUD

# Game state
var current_biome: GameEnums.BiomeType = GameEnums.BiomeType.FOREST
var is_game_active: bool = false

func _ready() -> void:
	_initialize_scene()
	_connect_events()
	_start_game()

func _initialize_scene() -> void:
	# Set up the scene
	print("Main scene initialized")

	# Initialize game systems
	_initialize_game_systems()

func _initialize_game_systems() -> void:
	# Initialize systems that need scene references
	if enemy_spawner and enemy_spawner.has_method("initialize"):
		enemy_spawner.initialize(self)

	if loot_manager and loot_manager.has_method("initialize"):
		loot_manager.initialize(self)

	if wave_manager and wave_manager.has_method("initialize"):
		wave_manager.initialize(self)

	if skill_tree:
		# Skill tree doesn't need scene reference
		pass

	if effect_manager and effect_manager.has_method("initialize"):
		effect_manager.initialize(self)

	if audio_manager:
		# Audio manager is global, no scene reference needed
		pass

	if biome_manager and biome_manager.has_method("initialize"):
		biome_manager.initialize(self)

func _connect_events() -> void:
	# Connect to global events
	EventBus.game_started.connect(_on_game_started)
	EventBus.game_paused.connect(_on_game_paused)
	EventBus.game_resumed.connect(_on_game_resumed)
	EventBus.run_started.connect(_on_run_started)
	EventBus.run_completed.connect(_on_run_completed)
	EventBus.player_died.connect(_on_player_died)
	EventBus.wave_completed.connect(_on_wave_completed)

func _start_game() -> void:
	# Start the initial game state
	is_game_active = true
	EventBus.game_started.emit()
	
	# Start first run
	GameManager.start_new_run("forest")

## Game Flow Management
func _on_game_started() -> void:
	print("Game started in Main scene")
	is_game_active = true

func _on_game_paused() -> void:
	print("Game paused")
	is_game_active = false
	get_tree().paused = true

func _on_game_resumed() -> void:
	print("Game resumed")
	is_game_active = true
	get_tree().paused = false

func _on_run_started(biome: String) -> void:
	print("Run started in biome: %s" % biome)
	current_biome = _string_to_biome(biome)
	
	# Configure environment for biome
	_setup_biome_environment()
	
	# Start wave system
	if wave_manager and wave_manager.has_method("start_waves"):
		wave_manager.start_waves()

func _on_run_completed(success: bool, rewards: Array) -> void:
	print("Run completed - Success: %s, Rewards: %s" % [success, rewards])
	is_game_active = false
	
	# Show results screen
	_show_run_results(success, rewards)

func _on_player_died() -> void:
	print("Player died - ending run")
	is_game_active = false
	
	# End current run
	GameManager._complete_run(false)

func _on_wave_completed(wave_number: int) -> void:
	print("Wave %d completed" % wave_number)
	
	# Progress to next wave
	GameManager.complete_current_wave()

## Biome Management
func _string_to_biome(biome_string: String) -> GameEnums.BiomeType:
	match biome_string.to_lower():
		"forest":
			return GameEnums.BiomeType.FOREST
		"desert":
			return GameEnums.BiomeType.DESERT
		"ice":
			return GameEnums.BiomeType.ICE
		"volcano":
			return GameEnums.BiomeType.VOLCANO
		"shadow":
			return GameEnums.BiomeType.SHADOW
		"celestial":
			return GameEnums.BiomeType.CELESTIAL
		_:
			return GameEnums.BiomeType.FOREST

func _setup_biome_environment() -> void:
	# Configure lighting and environment based on biome
	var light = $Environment/DirectionalLight3D
	
	match current_biome:
		GameEnums.BiomeType.FOREST:
			light.light_color = Color.WHITE
			light.light_energy = 1.0
		
		GameEnums.BiomeType.DESERT:
			light.light_color = Color.YELLOW
			light.light_energy = 1.2
		
		GameEnums.BiomeType.ICE:
			light.light_color = Color.CYAN
			light.light_energy = 0.8
		
		GameEnums.BiomeType.VOLCANO:
			light.light_color = Color.RED
			light.light_energy = 1.1
		
		GameEnums.BiomeType.SHADOW:
			light.light_color = Color.PURPLE
			light.light_energy = 0.6
		
		GameEnums.BiomeType.CELESTIAL:
			light.light_color = Color.GOLD
			light.light_energy = 1.3

## Results and UI
func _show_run_results(success: bool, rewards: Array) -> void:
	# This would show a results screen
	# For now, just print results
	print("=== Run Results ===")
	print("Success: %s" % success)
	print("Rewards:")
	for reward in rewards:
		print("  - %s: %s" % [reward.type, reward.amount])
	print("==================")
	
	# Auto-restart for demo purposes
	get_tree().create_timer(3.0).timeout.connect(_restart_run)

func _restart_run() -> void:
	# Reset player state
	if player and player.stats:
		player.stats.reset_to_full()
	
	# Start new run
	GameManager.start_new_run("forest")

## Input Handling
func _input(event: InputEvent) -> void:
	if event.is_action_pressed("ui_cancel"):
		_toggle_pause()

func _toggle_pause() -> void:
	if is_game_active:
		EventBus.game_paused.emit()
	else:
		EventBus.game_resumed.emit()

## Utility Functions
func get_player() -> Player:
	return player

func get_player_position() -> Vector3:
	return player.global_position if player else Vector3.ZERO

func spawn_effect_at_position(effect_name: String, position: Vector3) -> void:
	# Spawn visual effect at world position
	print("Spawning effect '%s' at %s" % [effect_name, position])
	
	# This would create actual particle effects
	# For now, just emit the event
	EventBus.effect_requested.emit(effect_name, position)

func get_enemies_in_range(center: Vector3, range: float) -> Array:
	var enemies = get_tree().get_nodes_in_group("enemies")
	var enemies_in_range = []
	
	for enemy in enemies:
		if enemy is Node3D:
			var distance = center.distance_to(enemy.global_position)
			if distance <= range:
				enemies_in_range.append(enemy)
	
	return enemies_in_range

func get_random_spawn_position() -> Vector3:
	# Generate random spawn position around the arena
	var angle = randf() * TAU
	var distance = randf_range(8.0, 15.0)
	var x = cos(angle) * distance
	var z = sin(angle) * distance
	
	return Vector3(x, 1.0, z)

## Debug Functions
func debug_spawn_enemy() -> void:
	if enemy_spawner and enemy_spawner.has_method("spawn_enemy"):
		var spawn_pos = get_random_spawn_position()
		enemy_spawner.spawn_enemy("grunt", spawn_pos)

func debug_spawn_loot() -> void:
	if loot_manager and loot_manager.has_method("spawn_loot"):
		var spawn_pos = get_player_position() + Vector3(randf_range(-2, 2), 0, randf_range(-2, 2))
		loot_manager.spawn_loot(spawn_pos, GameEnums.ItemRarity.RARE)

func debug_print_scene_state() -> void:
	print("=== Scene Debug Info ===")
	print("Current Biome: %s" % GameEnums.BiomeType.keys()[current_biome])
	print("Game Active: %s" % is_game_active)
	print("Player Position: %s" % get_player_position())
	print("Enemies Count: %d" % get_tree().get_nodes_in_group("enemies").size())
	print("========================")

## Cleanup
func _exit_tree() -> void:
	# Clean up any resources
	print("Main scene cleanup")
