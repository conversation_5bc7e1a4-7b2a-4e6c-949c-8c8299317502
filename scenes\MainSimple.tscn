[gd_scene load_steps=9 format=3 uid="uid://bqxvnhqxqxqxs"]

[ext_resource type="Script" path="res://scenes/MainSimple.gd" id="1"]
[ext_resource type="Script" path="res://player/PlayerSimple.gd" id="2"]
[ext_resource type="Script" path="res://player/PlayerStats.gd" id="3"]
[ext_resource type="Script" path="res://player/PlayerInput.gd" id="4"]
[ext_resource type="Script" path="res://player/CameraController.gd" id="5"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_1"]
radius = 0.5
height = 2.0

[sub_resource type="CapsuleMesh" id="CapsuleMesh_1"]
radius = 0.5
height = 2.0

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(20, 1, 20)

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(1, 0.1, 1)

[node name="Main" type="Node3D"]
script = ExtResource("1")

[node name="Environment" type="Node3D" parent="."]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 1.0
shadow_enabled = true

[node name="Ground" type="StaticBody3D" parent="Environment"]
collision_layer = 4
collision_mask = 0

[node name="GroundMesh" type="MeshInstance3D" parent="Environment/Ground"]
transform = Transform3D(20, 0, 0, 0, 1, 0, 0, 0, 20, 0, 0, 0)
mesh = SubResource("BoxMesh_1")

[node name="GroundCollision" type="CollisionShape3D" parent="Environment/Ground"]
transform = Transform3D(20, 0, 0, 0, 1, 0, 0, 0, 20, 0, 0, 0)
shape = SubResource("BoxShape3D_1")

[node name="Player" type="CharacterBody3D" parent="." groups=["player"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
collision_layer = 1
collision_mask = 6
script = ExtResource("2")

[node name="PlayerMesh" type="MeshInstance3D" parent="Player"]
mesh = SubResource("CapsuleMesh_1")

[node name="PlayerCollision" type="CollisionShape3D" parent="Player"]
shape = SubResource("CapsuleShape3D_1")

[node name="PlayerStats" type="Node" parent="Player"]
script = ExtResource("3")

[node name="PlayerInput" type="Node" parent="Player"]
script = ExtResource("4")

[node name="CameraController" type="Node3D" parent="Player"]
script = ExtResource("5")

[node name="CameraPivot" type="Node3D" parent="Player/CameraController"]
transform = Transform3D(1, 0, 0, 0, 0.707107, 0.707107, 0, -0.707107, 0.707107, 0, 5, 5)

[node name="Camera3D" type="Camera3D" parent="Player/CameraController/CameraPivot"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 8)
fov = 60.0

[node name="GameSystems" type="Node" parent="."]

[node name="EnemySpawner" type="Node" parent="GameSystems"]

[node name="LootManager" type="Node" parent="GameSystems"]

[node name="WaveManager" type="Node" parent="GameSystems"]

[node name="SkillTree" type="Node" parent="GameSystems"]

[node name="EffectManager" type="Node" parent="GameSystems"]

[node name="AudioManager" type="Node" parent="GameSystems"]

[node name="BiomeManager" type="Node" parent="GameSystems"]

[node name="UI" type="CanvasLayer" parent="."]

[node name="SimpleHUD" type="Control" parent="UI"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="DebugLabel" type="Label" parent="UI/SimpleHUD"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -100.0
offset_right = 400.0
offset_bottom = -20.0
text = "Project MUHero - Basic Version
WASD: Move Character
Right Mouse Drag: Rotate Camera
Mouse Wheel: Zoom Camera
1,2: Skills (placeholder)
Space: Dash (placeholder)"
vertical_alignment = 1
