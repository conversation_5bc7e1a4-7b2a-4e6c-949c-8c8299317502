class_name Camera<PERSON>ontroll<PERSON>
extends Node3D

## Camera controller for isometric-style camera with mouse/touch rotation
## Follows the player while allowing camera rotation around the player

@export var follow_speed: float = 10.0
@export var rotation_speed: float = 2.0
@export var mouse_sensitivity: float = 0.005
@export var touch_sensitivity: float = 0.01
@export var zoom_speed: float = 1.0
@export var min_zoom: float = 5.0
@export var max_zoom: float = 15.0

# Node references
@onready var camera_pivot: Node3D = $CameraPivot
@onready var camera: Camera3D = $CameraPivot/Camera3D

# Camera state
var target_position: Vector3 = Vector3.ZERO
var is_mouse_dragging: bool = false
var is_touch_dragging: bool = false
var last_mouse_position: Vector2 = Vector2.ZERO
var camera_rotation_x: float = -45.0  # Initial isometric angle
var camera_rotation_y: float = 0.0
var current_zoom: float = 8.0

# Touch tracking for camera
var camera_touch_index: int = -1
var last_touch_position: Vector2 = Vector2.ZERO

func _ready() -> void:
	_setup_camera()
	_connect_events()

func _setup_camera() -> void:
	# Set initial camera position and rotation
	if camera_pivot:
		camera_pivot.rotation_degrees.x = camera_rotation_x
		camera_pivot.rotation_degrees.y = camera_rotation_y
	
	if camera:
		camera.position.z = current_zoom
		camera.fov = 60.0

func _connect_events() -> void:
	# Connect to player position updates
	EventBus.player_position_changed.connect(_on_player_position_changed)

func _input(event: InputEvent) -> void:
	if event is InputEventMouseButton:
		_handle_mouse_button(event)
	elif event is InputEventMouseMotion:
		_handle_mouse_motion(event)
	elif event is InputEventScreenTouch:
		_handle_touch_input(event)
	elif event is InputEventScreenDrag:
		_handle_touch_drag(event)

func _handle_mouse_button(event: InputEventMouseButton) -> void:
	if event.button_index == MOUSE_BUTTON_RIGHT:
		if event.pressed:
			is_mouse_dragging = true
			last_mouse_position = event.position
			Input.mouse_mode = Input.MOUSE_MODE_CAPTURED
		else:
			is_mouse_dragging = false
			Input.mouse_mode = Input.MOUSE_MODE_VISIBLE
	elif event.button_index == MOUSE_BUTTON_WHEEL_UP and event.pressed:
		_zoom_camera(-zoom_speed)
	elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN and event.pressed:
		_zoom_camera(zoom_speed)

func _handle_mouse_motion(event: InputEventMouseMotion) -> void:
	if is_mouse_dragging:
		var delta_mouse = event.relative * mouse_sensitivity
		_rotate_camera(delta_mouse)

func _handle_touch_input(event: InputEventScreenTouch) -> void:
	# Only handle camera rotation if this is a secondary touch
	# (primary touch should be handled by PlayerInput for movement)
	if event.index == 1:  # Second finger
		if event.pressed:
			is_touch_dragging = true
			camera_touch_index = event.index
			last_touch_position = event.position
		else:
			if camera_touch_index == event.index:
				is_touch_dragging = false
				camera_touch_index = -1

func _handle_touch_drag(event: InputEventScreenDrag) -> void:
	if is_touch_dragging and event.index == camera_touch_index:
		var delta_touch = (event.position - last_touch_position) * touch_sensitivity
		_rotate_camera(delta_touch)
		last_touch_position = event.position

func _rotate_camera(delta: Vector2) -> void:
	# Rotate around Y axis (horizontal rotation)
	camera_rotation_y -= delta.x * rotation_speed
	
	# Rotate around X axis (vertical rotation) with limits
	camera_rotation_x -= delta.y * rotation_speed
	camera_rotation_x = clamp(camera_rotation_x, -80.0, -10.0)  # Keep isometric-ish view
	
	# Apply rotation to camera pivot
	if camera_pivot:
		camera_pivot.rotation_degrees.x = camera_rotation_x
		camera_pivot.rotation_degrees.y = camera_rotation_y

func _zoom_camera(zoom_delta: float) -> void:
	current_zoom += zoom_delta
	current_zoom = clamp(current_zoom, min_zoom, max_zoom)
	
	if camera:
		camera.position.z = current_zoom

func _physics_process(delta: float) -> void:
	_update_camera_position(delta)

func _update_camera_position(delta: float) -> void:
	if target_position != Vector3.ZERO:
		# Smoothly follow the target (player)
		global_position = global_position.lerp(target_position, follow_speed * delta)

func _on_player_position_changed(new_position: Vector3) -> void:
	target_position = new_position

## Public interface
func set_target_position(position: Vector3) -> void:
	target_position = position

func reset_camera_rotation() -> void:
	camera_rotation_x = -45.0
	camera_rotation_y = 0.0
	if camera_pivot:
		camera_pivot.rotation_degrees.x = camera_rotation_x
		camera_pivot.rotation_degrees.y = camera_rotation_y

func set_zoom(zoom: float) -> void:
	current_zoom = clamp(zoom, min_zoom, max_zoom)
	if camera:
		camera.position.z = current_zoom
